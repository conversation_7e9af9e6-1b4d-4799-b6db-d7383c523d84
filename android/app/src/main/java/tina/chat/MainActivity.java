package tina.chat;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.JavascriptInterface;
import android.widget.FrameLayout;

import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.getcapacitor.BridgeActivity;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import tina.chat.push.PushServiceManager;

import java.util.List;

public class MainActivity extends BridgeActivity {

    private static final String TAG = "MainActivity";

    private FunASRManager funASRManager;
    private NetworkDiagnosticManager networkManager;
    private NetworkDiagnosticView diagnosticView;
    private PushServiceManager pushServiceManager;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 注入权限管理到 WebView
        addPermissionInterface();

        // 注入FunASR接口到 WebView
        addFunASRInterface();

        // 初始化网络检测功能
        initNetworkDiagnostic();

        // 初始化推送服务
        initPushService();

        // get root view
        View rootView = getWindow().getDecorView().getRootView();
        ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
            var systemBars = WindowInsetsCompat.Type.systemBars();
            var ime = WindowInsetsCompat.Type.ime();

            // Get insets for both system bars and keyboard
            Insets allInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars() | WindowInsetsCompat.Type.ime());

            // Or if you want different handling:
            Insets systemBarInsets = insets.getInsets(systemBars);
            Insets imeInsets = insets.getInsets(ime);

            // Only apply keyboard insets to bottom padding
            v.setPadding(
                    systemBarInsets.left,
                    systemBarInsets.top,
                    systemBarInsets.right,
                    allInsets.bottom);
            return insets;
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // 清理FunASR资源
        if (funASRManager != null) {
            funASRManager.cleanup();
        }
        // 清理网络检测资源
        if (networkManager != null) {
            networkManager.cleanup();
        }
        // 清理推送资源
        if (pushServiceManager != null) {
            pushServiceManager.cleanup();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 开始网络监控
        if (networkManager != null) {
            networkManager.startMonitoring();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // 暂停网络监控以节省资源
        if (networkManager != null) {
            networkManager.stopMonitoring();
        }
    }

    private void addPermissionInterface() {
        // 等待 WebView 初始化完成后注入接口
        getBridge().getWebView().addJavascriptInterface(new PermissionInterface(), "AndroidPermissions");
    }

    private void addFunASRInterface() {
        // 创建FunASR管理器并注入接口
        funASRManager = new FunASRManager(this, getBridge().getWebView());
        getBridge().getWebView().addJavascriptInterface(funASRManager, "AndroidFunASR");
    }

    private void initNetworkDiagnostic() {
        // 创建网络检测管理器
        networkManager = new NetworkDiagnosticManager(this);

        // 创建诊断视图
        diagnosticView = new NetworkDiagnosticView(this);

        // 设置重试按钮点击监听
        diagnosticView.setOnRetryClickListener(() -> {
            // 立即重新检查网络状态
            networkManager.startMonitoring();
        });

        // 设置网络状态监听
        networkManager.setNetworkStatusListener(new NetworkDiagnosticManager.NetworkStatusListener() {
            @Override
            public void onNetworkStatusChanged(NetworkDiagnosticManager.NetworkStatus status) {
                runOnUiThread(() -> {
                    if (status.isFullyConnected()) {
                        // 网络完全正常，隐藏诊断页面
                        hideDiagnosticView();
                    } else {
                        // 网络异常，显示诊断页面并更新状态
                        diagnosticView.updateNetworkStatus(status);
                        showDiagnosticView();
                    }
                });
            }

            @Override
            public void onNetworkRestored() {
                runOnUiThread(() -> {
                    // 显示网络恢复消息
                    diagnosticView.showNetworkRestored();

                    // 刷新WebView
                    refreshWebView();

                    // 延迟隐藏诊断页面
                    new android.os.Handler().postDelayed(() -> {
                        hideDiagnosticView();
                    }, 3000);
                });
            }
        });

        // 将诊断视图添加到根布局
        addDiagnosticViewToRoot();
    }

    private void addDiagnosticViewToRoot() {
        // 获取根视图容器
        ViewGroup rootView = (ViewGroup) getWindow().getDecorView();

        // 创建FrameLayout作为容器
        FrameLayout container = new FrameLayout(this);
        FrameLayout.LayoutParams containerParams = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT);

        // 将诊断视图添加到容器中
        FrameLayout.LayoutParams diagnosticParams = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT);
        container.addView(diagnosticView, diagnosticParams);

        // 将容器添加到根视图
        rootView.addView(container, containerParams);
    }

    private void showDiagnosticView() {
        if (diagnosticView != null) {
            diagnosticView.showWithAnimation();
        }
    }

    private void hideDiagnosticView() {
        if (diagnosticView != null) {
            diagnosticView.hideWithAnimation();
        }
    }

    private void refreshWebView() {
        if (getBridge() != null && getBridge().getWebView() != null) {
            getBridge().getWebView().post(() -> {
                getBridge().getWebView().reload();
            });
        }
    }

    /**
     * 初始化推送服务
     * 现在只需要一行代码即可完成所有推送相关的初始化工作
     */
    private void initPushService() {
        pushServiceManager = PushServiceManager.getInstance(this);
        pushServiceManager.initializePushService();
    }

    public class PermissionInterface {

        @JavascriptInterface
        public void checkMicrophonePermission(String callbackName) {
            runOnUiThread(() -> {
                boolean hasPermission = XXPermissions.isGranted(MainActivity.this, Permission.RECORD_AUDIO);

                String result = String.format(
                        "{ \"hasPermission\": %s, \"permission\": \"microphone\" }",
                        hasPermission);

                executeCallback(callbackName, result);
            });
        }

        @JavascriptInterface
        public void requestMicrophonePermission(String callbackName) {
            runOnUiThread(() -> {
                XXPermissions.with(MainActivity.this)
                        .permission(Permission.RECORD_AUDIO)
                        .request(new OnPermissionCallback() {
                            @Override
                            public void onGranted(List<String> permissions, boolean allGranted) {
                                String result = String.format(
                                        "{ \"granted\": true, \"permission\": \"microphone\", \"message\": \"麦克风权限已授权\" }",
                                        allGranted);
                                executeCallback(callbackName, result);
                            }

                            @Override
                            public void onDenied(List<String> permissions, boolean doNotAskAgain) {
                                String message = doNotAskAgain ? "麦克风权限已被永久拒绝，请前往设置页面手动开启" : "麦克风权限被拒绝";

                                String result = String.format(
                                        "{ \"granted\": false, \"permission\": \"microphone\", \"doNotAskAgain\": %s, \"message\": \"%s\" }",
                                        doNotAskAgain, message);
                                executeCallback(callbackName, result);
                            }
                        });
            });
        }

        @JavascriptInterface
        public void openAppSettings(String callbackName) {
            runOnUiThread(() -> {
                XXPermissions.startPermissionActivity(MainActivity.this);

                String result = "{ \"success\": true, \"message\": \"已跳转到应用设置页面\" }";
                executeCallback(callbackName, result);
            });
        }
    }

    private void executeCallback(String callbackName, String result) {
        String script = String.format("if(window.%s) { window.%s(%s); }",
                callbackName, callbackName, result);

        getBridge().getWebView().post(() -> {
            getBridge().getWebView().evaluateJavascript(script, null);
        });
    }
}
