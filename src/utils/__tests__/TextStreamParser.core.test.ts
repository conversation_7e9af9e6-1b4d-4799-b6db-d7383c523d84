import { describe, it, expect } from 'vitest'
import { TextStreamParser } from '../TextStreamParser'

describe('TextStreamParser - Core Functionality', () => {
  describe('Chunk Separator Tests', () => {
    it('should split content by --tina-chunk-- separator', () => {
      const parser = new TextStreamParser('test')
      const input = 'Hello world--tina-chunk--This is second chunk'
      const results = parser.processText(input, true)

      expect(results).toHaveLength(2)
      expect(results[0]).toEqual({
        message_id_type: 'test-0:text',
        content: 'Hello world',
      })
      expect(results[1]).toEqual({
        message_id_type: 'test-1:text',
        content: 'This is second chunk',
      })
    })

    it('should handle streaming with chunk separator correctly', () => {
      const parser = new TextStreamParser('test3')

      const chunks = [
        '第一段内容',
        '--tina-chu',
        'nk--第二段',
        '内容--tina-chunk--',
        '第三段内容',
      ]

      let allResults: any[] = []
      chunks.forEach((chunk, index) => {
        const isLast = index === chunks.length - 1
        const results = parser.processText(chunk, isLast)
        allResults.push(...results)
      })

      // 获取最终的文本结果（去重，只取每个segment的最后一个结果）
      const finalResults = allResults.reduce((acc, result) => {
        const segmentId = result.message_id_type.split(':')[0]
        acc[segmentId] = result
        return acc
      }, {})

      const finalResultsArray = Object.values(finalResults)

      // 现在应该正确分割为3段
      expect(finalResultsArray).toHaveLength(3)
      expect(finalResultsArray[0]).toEqual({
        message_id_type: 'test3-0:text',
        content: '第一段内容',
      })
      expect(finalResultsArray[1]).toEqual({
        message_id_type: 'test3-1:text',
        content: '第二段内容',
      })
      expect(finalResultsArray[2]).toEqual({
        message_id_type: 'test3-2:text',
        content: '第三段内容',
      })
    })

    it('should handle XML tags mixed with chunk separator', () => {
      const parser = new TextStreamParser('test4')
      const input =
        '普通文本内容--tina-chunk--<tina_task>任务内容</tina_task>--tina-chunk--更多文本'
      const results = parser.processText(input, true)

      expect(results).toHaveLength(3)
      expect(results[0]).toEqual({
        message_id_type: 'test4-0:text',
        content: '普通文本内容',
      })
      expect(results[1]).toEqual({
        message_id_type: 'test4-2:xml', // segment索引会跳跃，因为遇到分隔符会增加索引
        content: '<tina_task>任务内容</tina_task>',
      })
      expect(results[2]).toEqual({
        message_id_type: 'test4-4:text', // 同样，索引会跳跃
        content: '更多文本',
      })
    })

    it('should handle voice blocks mixed with chunk separator', () => {
      const parser = new TextStreamParser('test5')
      const input = `文本内容--tina-chunk--\`\`\`voice
语音内容
\`\`\`--tina-chunk--结束文本`
      const results = parser.processText(input, true)

      expect(results).toHaveLength(3)
      expect(results[0]).toEqual({
        message_id_type: 'test5-0:text',
        content: '文本内容',
      })
      expect(results[1]).toEqual({
        message_id_type: 'test5-2:voice', // segment索引会跳跃
        content: '语音内容```',
      })
      expect(results[2]).toEqual({
        message_id_type: 'test5-4:text', // segment索引会跳跃
        content: '结束文本',
      })
    })
  })

  describe('XML Tag Recognition', () => {
    it('should correctly identify known XML tags', () => {
      const knownTags = [
        '<user_maybe_say>用户可能说的话</user_maybe_say>',
        '<card type="voice">语音卡片</card>',
        '<tina_task>任务标签</tina_task>',
        '<tina_analyze>分析标签</tina_analyze>',
        '<ina_analyze rank="8">INA分析</ina_analyze>',
        '<tina_memory>记忆标签</tina_memory>',
        '<ina_memory>INA记忆</ina_memory>',
        '<analyze>通用分析</analyze>',
      ]

      knownTags.forEach((tag, index) => {
        const parser = new TextStreamParser(`test-xml-${index}`)
        const input = `前置文本${tag}后置文本`
        const results = parser.processText(input, true)

        const xmlResults = results.filter((r) =>
          r.message_id_type.includes(':xml'),
        )
        expect(xmlResults).toHaveLength(1)
        expect(xmlResults[0].content).toBe(tag)
      })
    })

    it('should treat unknown XML tags as text', () => {
      const unknownTags = [
        '<unknown>未知标签</unknown>',
        '<div>HTML标签</div>',
        '<custom attr="value">自定义标签</custom>',
      ]

      unknownTags.forEach((tag, index) => {
        const parser = new TextStreamParser(`test-unknown-${index}`)
        const input = `前置文本${tag}后置文本`
        const results = parser.processText(input, true)

        const xmlResults = results.filter((r) =>
          r.message_id_type.includes(':xml'),
        )
        expect(xmlResults).toHaveLength(0)

        const textResults = results.filter((r) =>
          r.message_id_type.includes(':text'),
        )
        const allText = textResults.map((r) => r.content).join('')
        expect(allText).toContain(tag)
      })
    })
  })

  describe('Voice Block Processing', () => {
    it('should correctly process voice blocks', () => {
      const parser = new TextStreamParser('test-voice')
      const input = `前置文本\`\`\`voice
这是语音内容
多行语音
\`\`\`后置文本`
      const results = parser.processText(input, true)

      const voiceResults = results.filter((r) =>
        r.message_id_type.includes(':voice'),
      )
      expect(voiceResults).toHaveLength(1)
      expect(voiceResults[0].content).toBe('这是语音内容\n多行语音```')

      const textResults = results.filter((r) =>
        r.message_id_type.includes(':text'),
      )
      expect(textResults).toHaveLength(2)
      expect(textResults[0].content).toBe('前置文本')
      expect(textResults[1].content).toBe('后置文本')
    })

    it('should handle voice blocks with XML tags inside', () => {
      const parser = new TextStreamParser('test-voice-xml')
      const input = `\`\`\`voice
语音内容
<card>卡片内容</card>
更多语音
\`\`\``
      const results = parser.processText(input, true)

      // voice块内的XML标签会被包含在voice内容中，而不是单独解析
      const voiceResults = results.filter((r) =>
        r.message_id_type.includes(':voice'),
      )

      expect(voiceResults).toHaveLength(1)
      expect(voiceResults[0].content).toContain('<card>卡片内容</card>')
      expect(voiceResults[0].content).toContain('语音内容')
      expect(voiceResults[0].content).toContain('更多语音')
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty input', () => {
      const parser = new TextStreamParser('test-empty')
      const results = parser.processText('', true)
      expect(results).toHaveLength(0)
    })

    it('should handle input with only separators', () => {
      const parser = new TextStreamParser('test-separators')
      const results = parser.processText('--tina-chunk----tina-chunk--', true)
      expect(results).toHaveLength(0)
    })

    it('should handle malformed XML tags', () => {
      const parser = new TextStreamParser('test-malformed')
      const input =
        '<card>未闭合标签--tina-chunk--<user_maybe_say>正常标签</user_maybe_say>'
      const results = parser.processText(input, true)

      // 由于第一个<card>标签未闭合，整个内容会被当作文本处理
      // 这是当前解析器的行为：只有完整的XML标签才会被识别
      const textResults = results.filter((r) =>
        r.message_id_type.includes(':text'),
      )
      expect(textResults).toHaveLength(1)
      expect(textResults[0].content).toContain('<card>未闭合标签')
      expect(textResults[0].content).toContain(
        '<user_maybe_say>正常标签</user_maybe_say>',
      )
    })
  })
})
