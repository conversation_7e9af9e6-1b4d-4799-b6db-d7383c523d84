import { describe, it, expect } from 'vitest'
import { TextStreamParser } from '../TextStreamParser'

/**
 * Chunk 分隔符段落分割测试
 *
 * 测试 TextStreamParser 对 --tina-chunk-- 分隔符的段落分割功能
 * 重点验证：分隔符数量 + 1 = 段落数量
 */
describe('TextStreamParser - Chunk 分隔符段落分割', () => {
  // 辅助函数：提取不同的段落ID（segmentIndex）
  function extractUniqueSegments(outputs: any[]): Set<number> {
    const segments = new Set<number>()
    outputs.forEach((output) => {
      // message_id_type 格式: "baseId-segmentIndex:type"
      const match = output.message_id_type.match(/-(\d+):/)
      if (match) {
        segments.add(parseInt(match[1]))
      }
    })
    return segments
  }

  it('应该正确分割简单的分隔符内容', () => {
    const content = '开始--tina-chunk--中间--tina-chunk--结束'
    const parser = new TextStreamParser('simple-chunk-test')
    const outputs = parser.processText(content, true)

    const chunkCount = (content.match(/--tina-chunk--/g) || []).length
    const uniqueSegments = extractUniqueSegments(outputs)

    // 验证：2个分隔符应该产生3个段落
    expect(uniqueSegments.size).toBe(chunkCount + 1)
    expect(uniqueSegments.size).toBe(3)

    // 验证内容完整性
    const allContent = outputs.map((o) => o.content).join('')
    expect(allContent).toContain('开始')
    expect(allContent).toContain('中间')
    expect(allContent).toContain('结束')
    expect(allContent).not.toContain('--tina-chunk--') // 分隔符不应该出现在输出中
  })

  it('应该正确处理包含表格的复杂内容', () => {
    const content = `根：

--tina-chunk--

### **2025**
| 指标               | 2025年预测 | 常年平均值 | 对比情况 |
|--------------------|------------|------------|----------|
| 西北太平洋台风生成数 | 4-5个      | 3.8个      | **偏多** |
| 影响      | 2-3个      | 1.8个      | **偏多** |

--tina-chunk--

### **主要原因**
1. **海温偏高**：当成
2. **季风活跃**：西南强
3. **大气环流配置**：副行

--tina-chunk--

### **需注意**
- 台存在
- 7注华南、东南沿海

需？`

    const parser = new TextStreamParser('complex-chunk-test')
    const outputs = parser.processText(content, true)

    const chunkCount = (content.match(/--tina-chunk--/g) || []).length
    const uniqueSegments = extractUniqueSegments(outputs)

    // 验证：3个分隔符应该产生4个段落
    expect(uniqueSegments.size).toBe(chunkCount + 1)
    expect(uniqueSegments.size).toBe(4)

    // 验证关键内容存在
    const allContent = outputs.map((o) => o.content).join('')
    expect(allContent).toContain('根：')
    expect(allContent).toContain('### **2025**')
    expect(allContent).toContain('西北太平洋台风生成数')
    expect(allContent).toContain('### **主要原因**')
    expect(allContent).toContain('### **需注意**')
    expect(allContent).toContain('需？')
  })

  it('应该在表格中保留 <br> 标签而不分割段落', () => {
    const tableContent = `表格示例：

| 列1 | 列2 |
|-----|-----|
| 内容1<br>换行 | 内容2 |

--tina-chunk--

后续内容`

    const parser = new TextStreamParser('table-chunk-test')
    const outputs = parser.processText(tableContent, true)

    const chunkCount = (tableContent.match(/--tina-chunk--/g) || []).length
    const uniqueSegments = extractUniqueSegments(outputs)

    // 验证：1个分隔符应该产生2个段落
    expect(uniqueSegments.size).toBe(chunkCount + 1)
    expect(uniqueSegments.size).toBe(2)

    // 获取最终结果（去重，只取每个segment的最后一个结果）
    const finalResults = outputs.reduce((acc, result) => {
      const segmentId = result.message_id_type.split(':')[0]
      acc[segmentId] = result
      return acc
    }, {})

    const finalResultsArray = Object.values(finalResults)

    // 验证 <br> 标签被保留在表格中
    const tableOutputWithBr = finalResultsArray.find(
      (o: any) => o.content.includes('|') && o.content.includes('<br>'),
    )
    expect(tableOutputWithBr).toBeDefined()
    expect((tableOutputWithBr as any)!.content).toContain('内容1<br>换行')

    // 验证后续内容在新段落中
    const afterChunkOutput = finalResultsArray.find((o: any) =>
      o.content.includes('后续内容'),
    )
    expect(afterChunkOutput).toBeDefined()
  })

  it('应该正确处理流式输入中的分隔符分割', () => {
    const content = '前面--tina-chunk--后面'
    const parser = new TextStreamParser('stream-chunk-test')
    const allOutputs: any[] = []

    // 模拟流式输入，分块发送
    const chunks = ['前面--tina-chu', 'nk--后面']
    chunks.forEach((chunk, index) => {
      const isFinal = index === chunks.length - 1
      const outputs = parser.processText(chunk, isFinal)
      allOutputs.push(...outputs)
    })

    // 获取最终结果
    const finalResults = allOutputs.reduce((acc, result) => {
      const segmentId = result.message_id_type.split(':')[0]
      acc[segmentId] = result
      return acc
    }, {})

    const finalResultsArray = Object.values(finalResults)

    // 验证：1个分隔符应该产生2个段落
    expect(finalResultsArray).toHaveLength(2)

    // 验证内容完整性
    const allContent = finalResultsArray.map((o: any) => o.content).join('')
    expect(allContent).toContain('前面')
    expect(allContent).toContain('后面')
    expect(allContent).not.toContain('--tina-chunk--')
  })

  it('应该正确处理混合XML标签和分隔符的内容', () => {
    const content = `文本内容--tina-chunk--<tina_task>任务内容</tina_task>--tina-chunk--更多文本`
    const parser = new TextStreamParser('mixed-content-test')
    const outputs = parser.processText(content, true)

    const chunkCount = (content.match(/--tina-chunk--/g) || []).length
    const uniqueSegments = extractUniqueSegments(outputs)

    // 验证段落分割
    expect(uniqueSegments.size).toBeGreaterThanOrEqual(2)

    // 验证XML标签被正确识别
    const xmlOutputs = outputs.filter((o) => o.message_id_type.includes(':xml'))
    expect(xmlOutputs).toHaveLength(1)
    expect(xmlOutputs[0].content).toBe('<tina_task>任务内容</tina_task>')

    // 验证文本内容
    const textOutputs = outputs.filter((o) =>
      o.message_id_type.includes(':text'),
    )
    const allTextContent = textOutputs.map((o) => o.content).join('')
    expect(allTextContent).toContain('文本内容')
    expect(allTextContent).toContain('更多文本')
  })
})
