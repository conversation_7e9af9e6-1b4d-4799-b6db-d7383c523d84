import { describe, it, expect } from 'vitest'
import { TextStreamParser } from '../TextStreamParser'

describe('TextStreamParser - Chunk Separator Tests', () => {
  it('should handle chunk separator in markdown tables correctly', () => {
    const parser = new TextStreamParser('test')

    // 测试用例：包含markdown表格的完整文本，使用新的分隔符
    const markdownWithTable = `# 奥尔加·托卡尔丘克（<PERSON>）深度档案

## 核心身份
- **生卒**：1962年1月29日生于波兰苏莱胡夫
- **荣誉**：2018年诺贝尔文学奖得主（2019年补颁）、两次获波兰最高文学奖「尼刻奖」
- **职业**：小说家、心理学家、公共知识分子

## 创作特征
\`\`\`mermaid
graph TD
    A[创作风格] --> B[魔幻现实主义]
    A --> C[多声部叙事]
    A --> D[历史解构]
    B --> E[《雅各布之书》梦境叙事]
    C --> F[《航班》碎片化视角]
    D --> G[《太古和其他的时间》神话重写]
\`\`\`

## 关键作品年表
| 年份 | 作品（中文/波兰文） | 国际影响 |
|------|---------------------|----------|
| 1996 | 《太古和其他的时间》<br>《Prawiek i inne czasy》 | 波兰当代文学经典 |
| 2007 | 《航班》<br>《Bieguni》 | 获2018年布克国际奖 |
| 2014 | 《雅各布之书》<br>《Księgi Jakubowe》 | 诺奖重要评审依据 |
| 2022 | 《Empuzjon》 | 最新生态主题小说 |

--tina-chunk--

## 思想体系
1. **心理学背景**：华沙大学心理学学位影响其潜意识描写
2. **女性视角**：重构东欧历史中的边缘群体叙事
3. **生态关怀**：近年创作聚焦环境伦理议题

--tina-chunk--

## 争议事件
- 2019年因批评波兰民族主义遭极端分子威胁
- 《雅各布之书》涉及犹太教改革内容引发宗教讨论

**权威资源**：
- 诺奖演讲视频 [官网链接](https://www.nobelprize.org/prizes/literature/2018/tokarczuk/lecture/)
- 华沙大学作家档案 [数据库](http://rcin.org.pl/dlibra/publication/edition/168066)`

    // 模拟流式输入，分块发送
    const chunks = [
      '# 奥尔加·托卡尔丘克（Olga Tokarczuk）深度档案\n\n## 核心身份\n- **生卒**：1962年1月29日生于波兰苏莱胡夫\n- **荣誉**：2018年诺贝尔文学奖得主（2019年补颁）、两次获波兰最高文学奖「尼刻奖」\n- **职业**：小说家、心理学家、公共知识分子\n\n## 创作特征\n```mermaid\ngraph TD\n    A[创作风格] --> B[魔幻现实主义]\n    A --> C[多声部叙事]\n    A --> D[历史解构]\n    B --> E[《雅各布之书》梦境叙事]\n    C --> F[《航班》碎片化视角]\n    D --> G[《太古和其他的时间》神话重写]\n```\n\n## 关键作品年表\n| 年份 | 作品（中文/波兰文） | 国际影响 |\n|------|---------------------|----------|\n| 1996 | 《太古和其他的时间》<br>《Prawiek i inne czasy》 | 波兰当代文学经典 |\n| 2007 | 《航班》<br>《Bieguni》 | 获2018年布克国际奖 |\n| 2014 | 《雅各布之书》<br>《Księgi Jakubowe》 | 诺奖重要评审依据 |\n| 2022 | 《Empuzjon》 | 最新生态主题小说 |\n\n--tina-chu',
      'nk--\n\n## 思想体系\n1. **心理学背景**：华沙大学心理学学位影响其潜意识描写\n2. **女性视角**：重构东欧历史中的边缘群体叙事\n3. **生态关怀**：近年创作聚焦环境伦理议题\n\n--tina-chunk--\n\n## 争议事件\n- 2019年因批评波兰民族主义遭极端分子威胁\n- 《雅各布之书》涉及犹太教改革内容引发宗教讨论\n\n**权威资源**：\n- 诺奖演讲视频 [官网链接](https://www.nobelprize.org/prizes/literature/2018/tokarczuk/lecture/)\n- 华沙大学作家档案 [数据库](http://rcin.org.pl/dlibra/publication/edition/168066)',
    ]

    const allResults: any[] = []

    // 逐块处理
    chunks.forEach((chunk, index) => {
      const isLast = index === chunks.length - 1
      const results = parser.processText(chunk, isLast)
      allResults.push(...results)
    })

    // 获取最终的文本结果（去重，只取每个segment的最后一个结果）
    const finalResults = allResults.reduce((acc, result) => {
      const segmentId = result.message_id_type.split(':')[0]
      acc[segmentId] = result
      return acc
    }, {})

    const finalResultsArray = Object.values(finalResults)

    // 应该正确分割为3段
    expect(finalResultsArray).toHaveLength(3)

    // 验证第一段包含表格和<br>标签（不被误拆分）
    const firstSegment = finalResultsArray[0] as any
    expect(firstSegment.content).toContain(
      '| 年份 | 作品（中文/波兰文） | 国际影响 |',
    )
    expect(firstSegment.content).toContain('<br>')
    expect(firstSegment.content).toContain(
      '《太古和其他的时间》<br>《Prawiek i inne czasy》',
    )

    // 验证第二段包含思想体系
    const secondSegment = finalResultsArray[1] as any
    expect(secondSegment.content).toContain('## 思想体系')

    // 验证第三段包含争议事件
    const thirdSegment = finalResultsArray[2] as any
    expect(thirdSegment.content).toContain('## 争议事件')
  })

  it('should handle streaming with chunk separator in table context', () => {
    const parser = new TextStreamParser('test-stream')

    // 测试分隔符被分割的情况
    const chunks = [
      '表格内容：\n| 列1 | 列2 |\n|-----|-----|\n| 数据<br>换行 | 值 |\n\n--tina-chu',
      'nk--\n\n第二段内容',
      '--tina-chunk--\n\n第三段内容',
    ]

    const allResults: any[] = []
    chunks.forEach((chunk, index) => {
      const isLast = index === chunks.length - 1
      const results = parser.processText(chunk, isLast)
      allResults.push(...results)
    })

    // 获取最终结果
    const finalResults = allResults.reduce((acc, result) => {
      const segmentId = result.message_id_type.split(':')[0]
      acc[segmentId] = result
      return acc
    }, {})

    const finalResultsArray = Object.values(finalResults)

    // 应该正确分割为3段
    expect(finalResultsArray).toHaveLength(3)

    // 验证表格中的<br>标签没有被误拆分
    const firstSegment = finalResultsArray[0] as any
    expect(firstSegment.content).toContain('数据<br>换行')
    expect(firstSegment.content).toContain('表格内容')

    // 验证其他段落正确分割
    const secondSegment = finalResultsArray[1] as any
    expect(secondSegment.content).toContain('第二段内容')

    const thirdSegment = finalResultsArray[2] as any
    expect(thirdSegment.content).toContain('第三段内容')
  })
})
