import { describe, expect, it } from 'vitest'
import { TextStreamParser } from '@/utils/TextStreamParser'

/**
 * TextStreamParser - Tailwind CSS 内容 Bug 测试
 *
 * 测试具体的 Tailwind CSS 内容中的 XML 标签解析问题
 */
describe('TextStreamParser - Tailwind CSS 内容 Bug 测试', () => {
  // 实际的问题内容（已解码 Unicode 转义字符）
  const tailwindContent = `**「Tailwind CSS 极简引入指南」** 💻  

\`\`\`html
<!DOCTYPE html>
</html>
\`\`\`

**彩蛋技巧** 🎨  
- 快速调试：在浏览器控制台输入 \`tailwind.config\` 查看当前配置  
- 您喜欢的黑豹乐队色系已预设：  
  \`\`\`css
  .bg-black-panther { background-color: #1a1a1a }
  \`\`\`

<card type="link" title="Tailwind官方文档" img="https://tina-test.bfbdata.com"></card>

<user_maybe_say></user_maybe_say>  

<ina_analyze rank="9">  
用户技术需求与视觉偏好结合  
</ina_analyze>  

<memory>  
保存用户自定义颜色偏好  
</memory>`

  describe('非流式解析测试', () => {
    it('应该正确识别所有 XML 标签，忽略代码块内的 HTML 标签', () => {
      const parser = new TextStreamParser('tailwind-non-stream')
      const allOutputs = parser.processText(tailwindContent, true)

      // 分类统计输出
      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      console.log('=== 非流式解析结果统计 ===')
      console.log(`文本输出数量: ${textOutputs.length}`)
      console.log(`XML输出数量: ${xmlOutputs.length}`)

      console.log('=== XML 输出详情 ===')
      xmlOutputs.forEach((output, index) => {
        console.log(`${index + 1}. ${output.message_id_type}:`)
        console.log(`   内容: ${output.content.substring(0, 100)}...`)
      })

      // 验证 XML 输出数量（应该有4个：card, user_maybe_say, ina_analyze, memory）
      expect(xmlOutputs.length).toBe(4)

      // 验证具体的 XML 标签
      const cardXml = xmlOutputs.find((o) =>
        o.content.includes('<card type="link"'),
      )
      expect(cardXml).toBeDefined()
      expect(cardXml!.content).toContain('Tailwind官方文档')

      const userMaybeSayXml = xmlOutputs.find((o) =>
        o.content.includes('<user_maybe_say>'),
      )
      expect(userMaybeSayXml).toBeDefined()

      const analyzeXml = xmlOutputs.find((o) =>
        o.content.includes('<ina_analyze'),
      )
      expect(analyzeXml).toBeDefined()
      expect(analyzeXml!.content).toContain('用户技术需求与视觉偏好结合')

      const memoryXml = xmlOutputs.find((o) => o.content.includes('<memory>'))
      expect(memoryXml).toBeDefined()
      expect(memoryXml!.content).toContain('保存用户自定义颜色偏好')

      // 验证代码块内的 HTML 标签没有被识别为 XML
      const htmlTagsInXml = xmlOutputs.some(
        (o) =>
          o.content.includes('<!DOCTYPE html>') ||
          o.content.includes('<html lang="zh-CN">') ||
          o.content.includes('<script src="https://cdn.tailwindcss.com">') ||
          o.content.includes('<body class="bg-gray-100'),
      )
      expect(htmlTagsInXml).toBe(false)

      console.log('✅ 非流式解析验证通过')
    })
  })

  describe('流式解析测试', () => {
    it('应该在分块输入时正确识别 XML 标签', () => {
      const parser = new TextStreamParser('tailwind-stream')
      const allOutputs: any[] = []

      // 模拟流式输入，每次输入100个字符
      const chunkSize = 10
      for (let i = 0; i < tailwindContent.length; i += chunkSize) {
        const chunk = tailwindContent.substring(i, i + chunkSize)
        const isFinal = i + chunkSize >= tailwindContent.length
        const outputs = parser.processText(chunk, isFinal)
        allOutputs.push(...outputs)
      }

      // 最后一块设置 isFinal=true 确保所有内容都被处理
      if (allOutputs.length === 0) {
        // 如果没有输出，可能是因为最后一块没有设置isFinal
        const finalOutputs = parser.processText('', true)
        allOutputs.push(...finalOutputs)
      }

      // 分类统计输出
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      console.log('=== 流式解析结果统计 ===')
      console.log(`总输出数量: ${allOutputs.length}`)
      console.log(`XML输出数量: ${xmlOutputs.length}`)

      // 验证 XML 输出数量
      expect(xmlOutputs.length).toBe(4)

      // 验证具体的 XML 标签存在
      const cardExists = xmlOutputs.some((o) =>
        o.content.includes('<card type="link"'),
      )
      const userMaybeSayExists = xmlOutputs.some((o) =>
        o.content.includes('<user_maybe_say>'),
      )
      const analyzeExists = xmlOutputs.some((o) =>
        o.content.includes('<ina_analyze'),
      )
      const memoryExists = xmlOutputs.some((o) =>
        o.content.includes('<memory>'),
      )

      expect(cardExists).toBe(true)
      expect(userMaybeSayExists).toBe(true)
      expect(analyzeExists).toBe(true)
      expect(memoryExists).toBe(true)

      console.log('✅ 流式解析验证通过')
    })
  })

  describe('代码块检测验证', () => {
    it('应该正确区分代码块内外的标签', () => {
      const parser = new TextStreamParser('tailwind-codeblock')
      const allOutputs = parser.processText(tailwindContent, true)

      // 获取所有文本输出，检查代码块内容
      const textOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':text'),
      )
      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      // 验证核心功能：XML 标签被正确识别
      expect(xmlOutputs.length).toBe(4)

      // 验证代码块内容存在于文本输出中（简化验证）
      const allTextContent = textOutputs.map((o) => o.content).join('')

      // 至少应该包含一些代码块标记
      const hasCodeBlockMarkers =
        allTextContent.includes('```html') || allTextContent.includes('```css')
      expect(hasCodeBlockMarkers).toBe(true)

      // 验证代码块内的 HTML 标签没有被识别为 XML
      const htmlTagsInXml = xmlOutputs.some(
        (o) =>
          o.content.includes('<!DOCTYPE html>') ||
          o.content.includes('<html lang="zh-CN">') ||
          o.content.includes('<script src="https://cdn.tailwindcss.com">'),
      )
      expect(htmlTagsInXml).toBe(false)

      console.log('✅ 代码块检测验证通过')
    })
  })

  describe('边界情况测试', () => {
    it('应该正确处理紧邻代码块的 XML 标签', () => {
      // 测试代码块结束后紧接着的 XML 标签
      const testContent = `\`\`\`html
<div>HTML内容</div>
\`\`\`
<card type="test">紧邻的卡片</card>`

      const parser = new TextStreamParser('boundary-test')
      const allOutputs = parser.processText(testContent, true)

      const xmlOutputs = allOutputs.filter((o) =>
        o.message_id_type.includes(':xml'),
      )

      expect(xmlOutputs.length).toBe(1)
      expect(xmlOutputs[0].content).toContain(
        '<card type="test">紧邻的卡片</card>',
      )

      console.log('✅ 边界情况测试通过')
    })
  })
})
