export interface StreamOutput {
  message_id_type: string
  content: string
}

type ParserState = 'TEXT' | 'IN_XML' | 'IN_VOICE'
type ContentType = 'text' | 'xml' | 'voice'

const CHUNK_SEPARATOR = '--tina-chunk--'
const VOICE_START_TAG = '```voice\n'
const VOICE_END_REGEX = /\n\s*```\s*/

// 定义系统中实际使用的XML标签类型
const KNOWN_XML_TAGS = [
  'tina_task', // 任务相关标签
  'card', // 卡片标签
  'user_maybe_say', // 用户建议标签
  'tina_analyze', // 分析标签（用于测试和分析场景）
  'tina_memory', // 分析标签（用于测试和分析场景）
  'analyze',
  // 错误的标签不进入 text
  'ina_analyze',
  'ina_memory',
  'memory',
] as const

// 创建匹配已知XML标签的正则表达式
const XML_START_REGEX = new RegExp(
  `^<(${KNOWN_XML_TAGS.join('|')})(?:\\s+[^>]*)?>`,
)

export class TextStreamParser {
  private baseId: string
  private segmentIndex: number = 0
  private buffer: string = ''
  private currentState: ParserState = 'TEXT'

  // 状态相关数据
  private xmlTagName: string | null = null
  private xmlStartTag: string = '' // 只保存起始标签

  // 内容累加器
  private currentTextContent: string = ''
  private currentVoiceContent: string = ''

  constructor(baseId: string) {
    this.baseId = baseId
  }

  private getFullId(type: ContentType): string {
    return `${this.baseId}-${this.segmentIndex}:${type}`
  }

  // 开始一个新段落
  private startNewSegment() {
    this.segmentIndex++
    this.currentTextContent = ''
    this.currentVoiceContent = ''
  }

  /**
   * 流式/非流式调用同接口
   * @param text 新增文本
   * @param isFinal 是否是最后一块
   */
  processText(text: string, isFinal = false): StreamOutput[] {
    const results: StreamOutput[] = []
    this.buffer += text

    let progressMade = true
    while (progressMade) {
      progressMade = false

      switch (this.currentState) {
        case 'TEXT': {
          const firstAngle = this.buffer.indexOf('<')
          // 只检查 ```voice\n 格式的反引号，其他反引号当作普通文本处理
          const voiceStartIndex = this.buffer.indexOf(VOICE_START_TAG)
          // 检查分隔符位置
          const chunkSeparatorIndex = this.buffer.indexOf(CHUNK_SEPARATOR)

          // 将角括号、voice格式的反引号和分隔符作为潜在的分割点
          const potentialStarts = [
            firstAngle,
            voiceStartIndex,
            chunkSeparatorIndex,
          ].filter((i) => i !== -1)

          let safeText = ''
          const splitPoint =
            potentialStarts.length > 0
              ? Math.min(...potentialStarts)
              : this.buffer.length

          if (splitPoint > 0) {
            safeText = this.buffer.substring(0, splitPoint)
            this.buffer = this.buffer.substring(splitPoint)
            this.currentTextContent += safeText
            results.push({
              message_id_type: this.getFullId('text'),
              content: this.currentTextContent,
            })
            progressMade = true
          }

          // 处理分隔符
          if (this.buffer.startsWith(CHUNK_SEPARATOR)) {
            this.buffer = this.buffer.substring(CHUNK_SEPARATOR.length)
            this.startNewSegment()
            progressMade = true
            break
          }

          // 处理 voice 块开始
          if (this.buffer.startsWith(VOICE_START_TAG)) {
            this.startNewSegment()
            this.currentState = 'IN_VOICE'
            this.buffer = this.buffer.substring(VOICE_START_TAG.length)
            progressMade = true
            break
          }

          // 处理特殊 xml 标签
          const xmlMatch = this.isKnownXmlStartTag(this.buffer)
          if (xmlMatch) {
            this.startNewSegment()
            this.currentState = 'IN_XML'
            this.xmlTagName = xmlMatch[1]
            this.xmlStartTag = xmlMatch[0]
            this.buffer = this.buffer.substring(this.xmlStartTag.length)
            progressMade = true
            break
          }

          // 处理非特殊 xml 标签，归为普通文本
          if (this.isOtherXmlTag(this.buffer)) {
            const idx = this.buffer.indexOf('>')
            if (idx !== -1) {
              const xmlText = this.buffer.substring(0, idx + 1)
              this.currentTextContent += xmlText
              this.buffer = this.buffer.substring(idx + 1)
              results.push({
                message_id_type: this.getFullId('text'),
                content: this.currentTextContent,
              })
              progressMade = true
            } else {
              // 标签不完整，等待更多数据
            }
            break
          }

          // 没匹配到任何特殊，结束循环
          break
        }

        case 'IN_XML': {
          if (!this.xmlTagName) {
            // 理论上不可能，重置状态以避免死循环
            this.currentState = 'TEXT'
            this.startNewSegment()
            break
          }

          const endTag = `</${this.xmlTagName}>`
          const endTagIndex = this.buffer.indexOf(endTag)

          if (endTagIndex !== -1) {
            // 找到了！
            const xmlContent = this.buffer.substring(0, endTagIndex)
            const fullXmlBlock = this.xmlStartTag + xmlContent + endTag

            // 原子性输出
            results.push({
              message_id_type: this.getFullId('xml'),
              content: fullXmlBlock,
            })

            this.buffer = this.buffer.substring(endTagIndex + endTag.length)
            this.currentState = 'TEXT'
            this.startNewSegment()
            progressMade = true
          } else {
            // 未找到结束标签，等待更多数据
          }
          break
        }

        case 'IN_VOICE': {
          const fullContent = this.currentVoiceContent + this.buffer
          const endMatch = fullContent.match(VOICE_END_REGEX)

          if (endMatch && endMatch.index !== undefined) {
            // 找到结尾
            const voiceContent = fullContent.substring(0, endMatch.index)
            const voiceContentWithEndTag = voiceContent + endMatch[0].trim()

            // 将 voice 内容和结束标记添加到全局缓冲区
            this.globalContentBuffer += voiceContent + endMatch[0]

            results.push({
              message_id_type: this.getFullId('voice'),
              content: voiceContentWithEndTag,
            })
            this.buffer = fullContent.substring(
              endMatch.index + endMatch[0].length,
            )
            this.currentState = 'TEXT'
            this.startNewSegment()
            progressMade = true
          } else {
            // 没找到结尾，检查是否有XML标签需要先处理
            const xmlIdx = this.buffer.indexOf('<')
            if (xmlIdx !== -1) {
              const candidate = this.buffer.substring(xmlIdx)
              const xmlMatch = this.isKnownXmlStartTag(candidate)
              if (xmlMatch) {
                // 先输出 voice 中 xml 标签之前内容
                const beforeXml = this.buffer.substring(0, xmlIdx)
                if (beforeXml) {
                  this.currentVoiceContent += beforeXml
                  results.push({
                    message_id_type: this.getFullId('voice'),
                    content: this.currentVoiceContent,
                  })
                }
                this.buffer = this.buffer.substring(xmlIdx + xmlMatch[0].length)
                this.currentState = 'IN_XML'
                this.xmlTagName = xmlMatch[1]
                this.xmlStartTag = xmlMatch[0]
                this.startNewSegment()
                progressMade = true
              } else {
                // 没匹配特殊xml，作为普通内容继续
                this.currentVoiceContent += this.buffer
                results.push({
                  message_id_type: this.getFullId('voice'),
                  content: this.currentVoiceContent,
                })
                this.buffer = ''
              }
            } else if (this.buffer.length > 0) {
              // 全部作为 voice 内容输出
              this.currentVoiceContent += this.buffer
              results.push({
                message_id_type: this.getFullId('voice'),
                content: this.currentVoiceContent,
              })
              this.buffer = ''
            }
          }
          break
        }
      }
    }

    // 末尾时强制flush剩余内容
    if (isFinal && this.buffer.length > 0) {
      switch (this.currentState) {
        case 'TEXT':
          this.currentTextContent += this.buffer
          results.push({
            message_id_type: this.getFullId('text'),
            content: this.currentTextContent,
          })
          this.buffer = ''
          break
        case 'IN_VOICE':
          this.currentVoiceContent += this.buffer
          results.push({
            message_id_type: this.getFullId('voice'),
            content: this.currentVoiceContent,
          })
          this.buffer = ''
          break
        case 'IN_XML':
          // 不完整 xml 作为文本输出
          const incomplete = this.xmlStartTag + this.buffer
          results.push({
            message_id_type: this.getFullId('text'),
            content: incomplete,
          })
          this.buffer = ''
          break
      }
    }
    // 结束后开启新片段，让相同 id 的 message 可以产生新的段落
    if (isFinal) {
      this.startNewSegment()
    }

    return results
  }

  finalize(): StreamOutput[] {
    return this.processText('', true)
  }

  // 判断是否是已知xml标签起始
  private isKnownXmlStartTag(buffer: string): RegExpMatchArray | null {
    return buffer.match(XML_START_REGEX)
  }

  // 判断是否是其他xml标签起始（非已知标签）
  private isOtherXmlTag(buffer: string): boolean {
    if (buffer.startsWith('<')) {
      const genericMatch = buffer.match(/^<([a-zA-Z0-9\-]+)(?:\s[^>]*)?>/)
      if (genericMatch) {
        return !KNOWN_XML_TAGS.includes(genericMatch[1] as any)
      }
    }
    return false
  }
}
