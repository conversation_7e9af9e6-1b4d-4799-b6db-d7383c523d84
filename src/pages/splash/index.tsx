import { useEffect, useState } from 'react'
import { IonContent, IonPage, IonModal } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import { Toast } from '@capacitor/toast'
import { <PERSON><PERSON><PERSON> } from '@capacitor/browser'
import logo from '../../assets/logo.png'
import splashText from '../../assets/splash_text.png'
import useAuth from '../../tina/stores/authStore'
import { Capacitor } from '@capacitor/core'
import { AppUpdate, AppUpdateAvailability } from '@capawesome/capacitor-app-update'
import { betaInfo } from '../../data/betaInfo'
import { updateConfig } from '../../data/updateConfig'
import { checkForUpdates, shouldUpdate } from '../../utils/updateChecker'

/**
 * 移动端启动页面组件
 * 根据设计稿实现启动页面UI和动画效果
 */
export default function SplashPage() {
  const [progress, setProgress] = useState(0)
  const [loadingText, setLoadingText] = useState('正在初始化...')
  const [version, setVersion] = useState('')
  const [updateAvailable, setUpdateAvailable] = useState(false)
  const [updateType, setUpdateType] = useState<'immediate' | 'flexible' | null>(null)
  const [customApkUrl, setCustomApkUrl] = useState<string | null>(null)
  const [updateNotes, setUpdateNotes] = useState<string>('')
  const [showUpdateModal, setShowUpdateModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const history = useHistory()
  const auth = useAuth()

  useEffect(() => {
    // 版本号优先显示 updateConfig.versionName，其次 betaInfo.versionInfo，再次 package.json 的 version
    let v = updateConfig?.versionName || betaInfo?.versionInfo
    if (!v) {
      try {
        // @ts-ignore
        v = __APP_VERSION__
      } catch { }
    }
    setVersion(v || '')

    // 后台检查更新，不影响启动流程
    setTimeout(() => {
      // 先检测自定义升级服务器
      checkForUpdates()
        .then(data => {
          if (data && shouldUpdate(data.versionCode)) {
            setUpdateAvailable(true)
            setCustomApkUrl(data.apkUrl)
            setUpdateNotes(data.updateNotes)
            // 只有在启动完成后才显示更新对话框
            if (!isLoading) {
              setShowUpdateModal(true)
            }
          }
        })
        .catch((error) => {
          console.warn('检查更新失败:', error)
        })

      // 检查热更新（仅在原生环境下）
      if (Capacitor.isNativePlatform()) {
        AppUpdate.getAppUpdateInfo().then((result) => {
          if (result.updateAvailability === AppUpdateAvailability.UPDATE_AVAILABLE) {
            setUpdateAvailable(true)
            if (result.immediateUpdateAllowed) setUpdateType('immediate')
            else if (result.flexibleUpdateAllowed) setUpdateType('flexible')
            // 只有在启动完成后才显示更新对话框
            if (!isLoading) {
              setShowUpdateModal(true)
            }
          }
        })
      }
    }, 100) // 延迟0.1秒开始检查，确保不影响启动流程
  }, [isLoading])

  useEffect(() => {
    // 模拟启动加载过程
    const loadingSteps = [
      { progress: 20, text: '正在初始化...', delay: 500 },
      { progress: 45, text: '加载配置...', delay: 500 },
      { progress: 70, text: '连接服务器...', delay: 500 },
      { progress: 90, text: '准备就绪...', delay: 500 },
      { progress: 100, text: '启动完成', delay: 500 },
    ]

    let currentStep = 0
    const timer = setInterval(() => {
      if (currentStep < loadingSteps.length) {
        const step = loadingSteps[currentStep]
        setProgress(step.progress)
        setLoadingText(step.text)
        currentStep++
      } else {
        clearInterval(timer)
        setIsLoading(false)
        // 启动完成后，如果有更新则显示对话框，否则直接跳转
        if (updateAvailable) {
          setShowUpdateModal(true)
        } else {
          setTimeout(() => {
            if (auth.isLoggedIn()) {
              history.replace('/conversation/1')
            } else {
              history.replace('/home')
            }
          }, 500)
        }
      }
    }, 600)

    return () => clearInterval(timer)
  }, [history, auth, updateAvailable])

  // 监听更新对话框关闭
  useEffect(() => {
    if (!isLoading && !showUpdateModal && updateAvailable) {
      // 更新对话框关闭后跳转
      setTimeout(() => {
        if (auth.isLoggedIn()) {
          history.replace('/conversation/1')
        } else {
          history.replace('/home')
        }
      }, 500)
    }
  }, [isLoading, showUpdateModal, updateAvailable, history, auth])

  // 立即更新
  const handleImmediateUpdate = async () => {
    try {
      await AppUpdate.performImmediateUpdate()
    } catch (e) {
      alert('更新失败，请前往应用市场手动更新')
      await AppUpdate.openAppStore()
    }
  }

  // 弹性更新
  const handleFlexibleUpdate = async () => {
    try {
      await AppUpdate.startFlexibleUpdate()
      await AppUpdate.completeFlexibleUpdate()
    } catch (e) {
      alert('更新失败，请前往应用市场手动更新')
      await AppUpdate.openAppStore()
    }
  }

  // 立即更新（自定义服务器）
  const handleCustomUpdate = async () => {
    if (customApkUrl) {
      try {
        if (Capacitor.isNativePlatform()) {
          // 在原生环境中使用 Capacitor Browser 插件
          await Browser.open({
            url: customApkUrl,
            windowName: '_system' // 使用系统浏览器打开
          })
          await Toast.show({
            text: '正在打开浏览器下载...',
            duration: 'short',
            position: 'center'
          })
        } else {
          // 在 Web 环境中使用 window.open
          window.open(customApkUrl, '_blank')
        }
      } catch (error) {
        console.error('打开下载链接失败:', error)
        await Toast.show({
          text: '无法打开下载链接，请尝试复制链接手动下载',
          duration: 'long',
          position: 'center'
        })
      }
    }
  }

  // 手动下载 - 复制下载链接到剪贴板
  const handleManualDownload = async () => {
    if (customApkUrl) {
      try {
        await navigator.clipboard.writeText(customApkUrl)
        await Toast.show({
          text: '下载链接已复制到剪贴板',
          duration: 'short',
          position: 'center'
        })
      } catch (error) {
        // 如果剪贴板 API 不可用，使用备用方法
        const textArea = document.createElement('textarea')
        textArea.value = customApkUrl
        document.body.appendChild(textArea)
        textArea.select()
        try {
          document.execCommand('copy')
          await Toast.show({
            text: '下载链接已复制到剪贴板',
            duration: 'short',
            position: 'center'
          })
        } catch (fallbackError) {
          await Toast.show({
            text: '复制失败，请手动复制链接',
            duration: 'long',
            position: 'center'
          })
        }
        document.body.removeChild(textArea)
      }
    }
  }

  // 关闭更新对话框
  const handleCloseUpdateModal = () => {
    setShowUpdateModal(false)
    setUpdateAvailable(false)
    // 直接跳转，不重新执行动画
    setTimeout(() => {
      if (auth.isLoggedIn()) {
        history.replace('/conversation/1')
      } else {
        history.replace('/home')
      }
    }, 100)
  }

  return (
    <IonPage>
      <IonContent>
        <div
          className='mx-auto flex h-full w-full flex-col items-center text-white sm:max-w-md'
          style={{
            background: `linear-gradient(180deg,
              rgba(255, 255, 255, 0.31) 0%,
              rgba(182, 194, 202, 0.59) 70%,
              rgba(181, 193, 201, 0.64) 100%)`,
          }}
        >
          {/* 上半部分 - Logo 和文字区域 */}
          <div className='flex flex-1 flex-col items-center justify-center pb-10 pt-16'>
            {/* Logo 图片 */}
            <div className='animate-fade-in mb-6'>
              <div
                className='rounded-full p-6'
                style={{
                  backgroundColor: 'rgba(241, 233, 221, 1) ',
                  borderRadius: '60px',
                }}
              >
                <img
                  src={logo}
                  alt='Tina Logo'
                  className='h-28 w-48 object-contain'
                />
              </div>
            </div>

            {/* 文字图片 */}
            <div className='animate-fade-in'>
              <img
                src={splashText}
                alt='Tina Text'
                className='h-6 object-contain'
              />
            </div>
          </div>

          {/* 下半部分 - 进度条区域 */}
          <div className='flex flex-1 flex-col items-center justify-center'>
            {/* 分块式进度条 */}
            <div className='animate-fade-in flex space-x-3'>
              {[1, 2, 3, 4, 5].map((block) => (
                <div
                  key={block}
                  className='h-4 w-7 rounded-full transition-all duration-300 ease-out'
                  style={{
                    boxShadow: '0px 2px 20px 0px rgba(125,125,123,1)',
                    backgroundColor:
                      progress >= block * 20
                        ? 'rgba(239,236,227,1)'
                        : 'rgba(255, 255, 255, 0.3)',
                  }}
                />
              ))}
            </div>
          </div>

          {/* 底部版权信息和版本号 */}
          <div className='animate-fade-in absolute bottom-8 w-full text-center'>
            <p className='text-xs text-white opacity-75'>
              © 2025 Tina Chat. All rights reserved.
            </p>
            <p className='text-xs text-white opacity-75 mt-1'>
              版本号：{version}
            </p>
          </div>
        </div>

        {/* 更新对话框 - 全屏显示 */}
        <IonModal isOpen={showUpdateModal} backdropDismiss={false}>
          <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col">
            {/* 顶部装饰区域 */}
            <div className="relative h-32 bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 overflow-hidden">
              <div className="absolute inset-0 bg-black bg-opacity-10"></div>
              <div className="absolute top-10 -right-10 w-40 h-40 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute -bottom-5 -left-5 w-32 h-32 bg-white bg-opacity-10 rounded-full"></div>
              <div className="relative z-10 flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-3 rounded-2xl bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center shadow-lg">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                    </svg>
                  </div>
                  <h1 className="text-2xl font-bold text-white mb-1">有可用更新</h1>
                </div>
              </div>
            </div>

            {/* 主要内容区域 */}
            <div className="flex-1 flex flex-col px-6 py-8 min-h-0">
              {/* 更新内容区域 - 带滚动条 */}
              {updateNotes && (
                <div className="mb-8" style={{ height: 'calc(100vh - 400px)' }}>
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 h-full flex flex-col">
                    <div className="px-6 py-4 border-b border-gray-100 flex-shrink-0">
                      <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        更新内容
                      </h3>
                    </div>
                    <div className="flex-1 overflow-y-auto px-6 py-4 min-h-0">
                      <div className="text-gray-700 whitespace-pre-line leading-relaxed text-sm">
                        {updateNotes}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 如果没有更新内容，显示占位内容 */}
              {!updateNotes && (
                <div className="mb-8 flex items-center justify-center" style={{ height: 'calc(100vh - 600px)' }}>
                  <div className="text-center">
                    <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
                      <svg className="w-10 h-10 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">准备更新</h3>
                    <p className="text-gray-600 text-sm">我们为您准备了最新版本的应用</p>
                  </div>
                </div>
              )}

              {/* 按钮区域 */}
              <div className="space-y-4">
                {/* 主要更新按钮 */}
                {customApkUrl && (
                  <button
                    onClick={handleCustomUpdate}
                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 flex items-center justify-center space-x-3"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    <span>立即更新</span>
                  </button>
                )}
                {!customApkUrl && updateType === 'immediate' && (
                  <button
                    onClick={handleImmediateUpdate}
                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 flex items-center justify-center space-x-3"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span>立即更新</span>
                  </button>
                )}
                {!customApkUrl && updateType === 'flexible' && (
                  <button
                    onClick={handleFlexibleUpdate}
                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 flex items-center justify-center space-x-3"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707" />
                    </svg>
                    <span>后台更新</span>
                  </button>
                )}
                {/* 稍后再说按钮 */}
                <button
                  onClick={handleCloseUpdateModal}
                  className="w-full text-gray-500 hover:text-gray-700 font-medium py-3 px-6 rounded-2xl transition-all duration-300 hover:bg-gray-50 active:scale-95"
                >
                  稍后再说
                </button>
                {/* 手动下载按钮 */}
                {customApkUrl && (
                  <button
                    onClick={handleManualDownload}
                    className="w-full bg-white hover:bg-gray-50 text-gray-700 font-medium py-4 px-6 rounded-2xl transition-all duration-300 border-2 border-gray-200 hover:border-gray-300 flex items-center justify-center space-x-3 shadow-sm hover:shadow-md transform hover:scale-105 active:scale-95"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <span>复制下载链接</span>
                  </button>
                )}


              </div>
            </div>

            {/* 底部提示 */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
              <div className="flex items-center justify-center space-x-2">
                <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <p className="text-xs text-gray-600 text-center leading-relaxed">
                  为了您的使用体验和数据安全，建议及时更新到最新版本
                </p>
              </div>
            </div>
          </div>
        </IonModal>
      </IonContent>
    </IonPage>
  )
}
