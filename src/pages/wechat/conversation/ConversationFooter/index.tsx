import React, { useRef, useState, useEffect } from 'react'
import { isMobileOnly } from 'react-device-detect'
import { useAtom } from 'jotai'
import EmojiSmileSVG from '@/assets/emoji-smile.svg?react'
import KeyboardOutlinedSVG from '@/assets/keyboard-outlined.svg?react'
import MicSV<PERSON> from '@/assets/mic.svg?react'
import PlusCircleSVG from '@/assets/plus-circle.svg?react'
import SendSVG from '@/assets/send.svg?react'
import { useConversationAPI } from '../context'
import BottomPopup from './BottomPopup'
import EmojiPanel from './EmojiPanel'
import Input, { type InputRef } from './Input'
import VoiceInput from './VoiceInput'
import { showToast } from '@/wechatComponents/Toast'
import buttonSendModeAtom from '@/stateV2/buttonSendModeAtom'

// 本地存储键名
const VOICE_MODE_STORAGE_KEY = 'conversation_voice_mode'

// 检查是否支持语音输入（HTTPS或本地环境）
const isVoiceInputSupported = (): boolean => {
  // HTTPS 环境总是支持
  if (location.protocol === 'https:') {
    return true
  }

  // HTTP 环境下，只有本地地址支持
  if (location.protocol === 'http:') {
    const hostname = location.hostname
    return hostname === 'localhost' ||
      hostname === '127.0.0.1' ||
      hostname.startsWith('192.168.') ||
      hostname.startsWith('10.') ||
      hostname.startsWith('172.')
  }

  return false
}

// 从本地存储获取语音模式设置，默认为 true（如果支持的话）
const getVoiceModeFromStorage = (): boolean => {
  try {
    const stored = localStorage.getItem(VOICE_MODE_STORAGE_KEY)
    const defaultMode = isVoiceInputSupported() // 只有在支持时才默认使用语音模式
    return stored !== null ? JSON.parse(stored) : defaultMode
  } catch (error) {
    console.warn('读取语音模式设置失败:', error)
    return isVoiceInputSupported() // 只有在支持时才默认使用语音模式
  }
}

// 保存语音模式设置到本地存储
const saveVoiceModeToStorage = (isVoiceMode: boolean): void => {
  try {
    localStorage.setItem(VOICE_MODE_STORAGE_KEY, JSON.stringify(isVoiceMode))
  } catch (error) {
    console.warn('保存语音模式设置失败:', error)
  }
}

const ConversationFooter = () => {
  const [showEmojiPanel, setShowEmojiPanel] = useState(false)
  const [isVoiceMode, setIsVoiceMode] = useState(getVoiceModeFromStorage)
  const [buttonSendMode] = useAtom(buttonSendModeAtom)
  const [inputValue, setInputValue] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const inputRef = useRef<InputRef>(null)
  const { sendImageMessage, sendTextMessage } = useConversationAPI()

  // 监听 isVoiceMode 变化，保存到本地存储
  useEffect(() => {
    saveVoiceModeToStorage(isVoiceMode)
  }, [isVoiceMode])

  // 处理发送按钮点击
  const handleSendClick = () => {
    if (inputValue.trim()) {
      sendTextMessage(inputValue.trim())
      setInputValue('')
    }
  }

  // 判断是否有内容
  const hasContent = inputValue.trim().length > 0

  const inputComponentProps = {
    ...(isMobileOnly ? { showEmojiPanel, setShowEmojiPanel } : {}),
    value: inputValue,
    onChange: setInputValue,
    onSend: handleSendClick,
  }

  const handleImageSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const base64 = e.target?.result as string
        sendImageMessage(base64, file)
      }
      reader.readAsDataURL(file)
    }
    // 清空文件输入，允许重复选择同一文件
    event.target.value = ''
  }

  // 切换输入模式（文本/语音）
  const toggleInputMode = () => {
    // 检查是否支持语音输入
    if (!isVoiceMode && !isVoiceInputSupported()) {
      // 当前是文本模式，尝试切换到语音模式，但不支持语音输入
      showToast({
        type: 'error',
        content: '语音功能仅支持 HTTPS 安全环境'
      })
      return
    }

    const wasVoiceMode = isVoiceMode
    setIsVoiceMode(!isVoiceMode)

    // 切换到文本模式时关闭表情面板并自动获取焦点
    if (wasVoiceMode) {
      setShowEmojiPanel(false)
      // 延迟一下确保DOM更新完成后再获取焦点
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }

  // 处理语音识别结果
  const handleVoiceResult = (text: string) => {
    console.log('收到语音识别结果:', text)
    if (text.trim()) {
      sendTextMessage('<voice_message>' + text.trim() + '</voice_message>')
      console.log('发送语音消息:', text.trim())
    } else {
      // 识别结果为空，显示提示
      showToast({
        type: 'error',
        content: '未识别到内容'
      })
    }
  }

  // 处理语音错误
  const handleVoiceError = (error: string) => {
    console.error('语音录音错误:', error)
    // 可以在这里显示错误提示
  }

  return (
    <div className='flex flex-col'>
      {/* 悬浮式输入框容器 */}
      <div className='p-3 pb-6 bg-transparent'>
        <div 
          className='flex flex-col rounded-3xl shadow-lg border overflow-hidden backdrop-blur-md'
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.5)',
            borderColor: 'rgba(229, 231, 235, 0.3)'
          }}
        >
          <div className='flex w-full items-center space-x-2 p-1.5'>
          {/* 语音/键盘切换按钮 */}
          <div className="relative h-8 w-8 overflow-hidden">
            <div
              className={`absolute inset-0 transition-all duration-150 ease-out ${isVoiceMode && isVoiceInputSupported()
                // ? 'opacity-100 translate-y-0'
                // : 'opacity-0 translate-y-8'
                ? 'opacity-100'
                : 'opacity-0'
                }`}
            >
              <KeyboardOutlinedSVG
                fill='#000'
                className='h-8 w-8 cursor-pointer p-0.5'
                onClick={toggleInputMode}
              />
            </div>
            <div
              className={`absolute inset-0 transition-all duration-150 ease-out ${!(isVoiceMode && isVoiceInputSupported())
                // ? 'opacity-100 translate-y-0'
                // : 'opacity-0 translate-y-8'
                ? 'opacity-100'
                : 'opacity-0'
                }`}
            >
              <MicSVG
                fill='#000'
                className='h-8 w-8 cursor-pointer p-1'
                onClick={toggleInputMode}
              />
            </div>
          </div>

          {/* 输入区域 */}
          <div className="relative flex-1 overflow-hidden">
            {isVoiceMode && isVoiceInputSupported() ? (
              <div className="opacity-100">
                <VoiceInput
                  onResult={handleVoiceResult}
                  onError={handleVoiceError}
                />
              </div>
            ) : (
              <div className="opacity-100">
                <Input ref={inputRef} {...inputComponentProps} />
              </div>
            )}
          </div>

          {/* 表情和添加/发送按钮 */}
          {!(isVoiceMode && isVoiceInputSupported()) ? (
            // 文本模式：显示表情和添加/发送按钮
            <>
              {/* 隐藏表情按钮 - 功能保留 */}
              {/* 
              <div className="relative h-10 w-10">
                <div
                  className={`absolute inset-0 transition-all duration-200 ease-in-out ${showEmojiPanel
                    ? 'opacity-100 scale-100'
                    : 'opacity-0 scale-90'
                    }`}
                >
                  <KeyboardOutlinedSVG
                    fill='#000'
                    className='h-10 w-10 cursor-pointer p-0.5'
                    onClick={() => setShowEmojiPanel((v) => !v)}
                  />
                </div>
                <div
                  className={`absolute inset-0 transition-all duration-200 ease-in-out ${!showEmojiPanel
                    ? 'opacity-100 scale-100'
                    : 'opacity-0 scale-90'
                    }`}
                >
                  <EmojiSmileSVG
                    fill='#000'
                    className='h-10 w-10 cursor-pointer p-1'
                    onClick={() => setShowEmojiPanel((v) => !v)}
                  />
                </div>
              </div>
              */}

              {/* 添加/发送按钮 - 保留 */}
              <div className="relative h-8">
                {/* 隐藏的占位元素来保持容器宽度 */}
                <div className="invisible flex items-center">
                  {buttonSendMode && hasContent ? (
                    <button className='rounded bg-wechatBrand-3 px-2 py-0.5 text-sm font-medium'>
                      发送
                    </button>
                  ) : (
                    <div className='h-8 w-8' />
                  )}
                </div>
                {/* 实际显示的动画元素 */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-all duration-250 ease-in-out ${buttonSendMode && hasContent
                    ? 'opacity-100 scale-100 translate-x-0'
                    : 'opacity-0 scale-90 translate-x-2 pointer-events-none'
                    }`}
                >
                  <button
                    className='cursor-pointer rounded bg-wechatBrand-3 px-2 py-0.5 text-sm font-medium text-white hover:bg-wechatBrand-4 active:bg-wechatBrand-5 transition-colors duration-150'
                    onClick={handleSendClick}
                  >
                    发送
                  </button>
                </div>
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-all duration-250 ease-in-out ${!(buttonSendMode && hasContent)
                    ? 'opacity-100 scale-100 translate-x-0'
                    : 'opacity-0 scale-90 -translate-x-2 pointer-events-none'
                    }`}
                >
                  <PlusCircleSVG
                    fill='#000'
                    className='h-8 w-8 cursor-pointer p-0.5'
                    onClick={handleImageSelect}
                  />
                </div>
              </div>
            </>
          ) : (
            // 语音模式：只显示添加按钮
            <PlusCircleSVG
              fill='#000'
              className='h-8 w-8 cursor-pointer p-0.5'
              onClick={handleImageSelect}
            />
          )}

          <input
            ref={fileInputRef}
            type='file'
            accept='image/*'
            style={{ display: 'none' }}
            onChange={handleFileChange}
          />
          </div>
        </div>
      </div>

      {/* 表情面板 - 仅在文本模式显示，与悬浮式输入框保持一致 */}
      {!(isVoiceMode && isVoiceInputSupported()) && (
        <div className='px-3'>
          <BottomPopup show={showEmojiPanel}>
            <div 
              className='rounded-3xl shadow-lg border overflow-hidden backdrop-blur-md'
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                borderColor: 'rgba(229, 231, 235, 0.3)'
              }}
            >
              <EmojiPanel
                showEmojiPanel={showEmojiPanel}
                setShowEmojiPanel={setShowEmojiPanel}
              />
            </div>
          </BottomPopup>
        </div>
      )}
    </div>
  )
}

export default ConversationFooter
