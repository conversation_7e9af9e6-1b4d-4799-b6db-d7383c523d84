import React, { useCallback, useRef, useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { useVoiceRecording } from '@/hooks/useVoiceRecording'
import { showToast } from '@/wechatComponents/Toast'
import { ensureMicrophonePermission, checkMicrophonePermission } from '@/utils/androidPermissions'

// 在文件顶部插入样式
const waveformStyle = `
.waveform-container {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 50px;
}
.waveform-bubble {
    background-color: #67e35b;
    padding: 20px 35px;
    border-radius: 15px;
    position: relative;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}
.waveform-bubble::after {
    content: '';
    position: absolute;
    bottom: -18px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 10px;
    border-style: solid;
    border-color: #67e35b transparent transparent transparent;
}
.waveform {
    display: flex;
    align-items: center;
    height: 30px;
}
.bar {
    width: 3px;
    height: 5px;
    background-color: rgba(0, 0, 0, 0.6);
    margin: 0 2px;
    border-radius: 2px;
    animation: pulse 1.2s ease-in-out infinite;
}
.bar:nth-child(1) { animation-delay: 0.1s; }
.bar:nth-child(2) { animation-delay: 0.3s; }
.bar:nth-child(3) { animation-delay: 0.5s; }
.bar:nth-child(4) { animation-delay: 0.2s; }
.bar:nth-child(5) { animation-delay: 0.4s; }
.bar:nth-child(6) { animation-delay: 0.1s; }
.bar:nth-child(7) { animation-delay: 0.3s; height: 15px; }
.bar:nth-child(8) { animation-delay: 0s; height: 20px; }
.bar:nth-child(9) { animation-delay: 0.2s; height: 25px; }
.bar:nth-child(10) { animation-delay: 0.4s; height: 20px; }
.bar:nth-child(11) { animation-delay: 0.1s; height: 15px; }
.bar:nth-child(12) { animation-delay: 0.3s; }
.bar:nth-child(13) { animation-delay: 0.5s; }
.bar:nth-child(14) { animation-delay: 0.2s; }
.bar:nth-child(15) { animation-delay: 0.4s; }
.bar-silent {
    animation: pulse-silent 1.2s ease-in-out infinite;
}
@keyframes pulse {
    0%, 100% { transform: scaleY(0.5); }
    50% { transform: scaleY(1.5); }
}
@keyframes pulse-silent {
    0%, 100% { transform: scaleY(0.3); }
    50% { transform: scaleY(0.6); }
}
.controls-arc-bg {
    position: absolute;
    width: 260%;
    height: 120%;
    left: 50%;
    bottom: -50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
}
.send-arc-active {
    background-color: rgba(7, 193, 96, 0.95) !important;
}
.circular-button {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}
.circular-button-text {
    font-size: 13px;
}
.circular-button-cancel {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
}
.circular-button-cancel.active {
    background-color: #ef4444;
    color: white;
    transform: scale(1.1);
}
.circular-button-text {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
}
.circular-button-text.active {
    background-color: #3b82f6;
    color: white;
    transform: scale(1.1);
}
`;

interface VoiceInputProps {
  onResult: (text: string) => void
  onError?: (error: string) => void
}

// 全屏语音面板的操作区域
const PANEL_STATUS = {
  SEND: 'SEND',
  CANCEL: 'CANCEL',
  TEXT: 'TEXT',
} as const
type PanelStatus = typeof PANEL_STATUS[keyof typeof PANEL_STATUS] | ''

// ========== VoiceTextEditModal 独立组件 ========== //
interface VoiceTextEditModalProps {
  show: boolean
  originText: string
  onSend: (finalText: string) => void
  onCancel: () => void
}

const VoiceTextEditModal: React.FC<VoiceTextEditModalProps> = ({ show, originText, onSend, onCancel }) => {
  const [finalText, setFinalText] = useState(originText)
  const [aiVariants, setAiVariants] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const aiCount = 4
  const aiVariantRefs = useRef<string[]>([])
  const bubbleRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!show) return
    setFinalText(originText)
    setAiVariants([])
    setLoading(true)
    setError(null)
    aiVariantRefs.current = []
    let currentVariant = ''
    let currentIndex = 0
    let finished = false
    let timer: NodeJS.Timeout | null = null
    import('@/tina/lib/generate-text-variants').then(({ generateTextVariants }) => {
      generateTextVariants({
        text: originText,
        onData: (chunk, idx) => {
          currentVariant += chunk
          if (chunk.includes('\n')) {
            const lines = currentVariant.split(/\n+/)
            lines.forEach((line, i) => {
              if (line.trim()) {
                aiVariantRefs.current.push(line.trim())
                setAiVariants([...aiVariantRefs.current])
                currentIndex++
              }
            })
            currentVariant = ''
          }
          if (aiVariantRefs.current.length >= aiCount) {
            finished = true
            setLoading(false)
            if (timer) clearTimeout(timer)
          }
        },
        onError: (err) => {
          setError(typeof err === 'string' ? err : (err?.message || 'AI 处理失败'))
          setLoading(false)
        },
        onDone: () => {
          setLoading(false)
          finished = true
        },
      })
      timer = setTimeout(() => {
        if (!finished) setLoading(false)
      }, 8000)
    })
    // eslint-disable-next-line
  }, [show, originText])

  // 自动聚焦气泡
  useEffect(() => {
    if (show && bubbleRef.current) {
      bubbleRef.current.focus()
      // 将光标移到末尾
      const el = bubbleRef.current
      if (el) {
        const range = document.createRange()
        range.selectNodeContents(el)
        range.collapse(false)
        const sel = window.getSelection()
        sel?.removeAllRanges()
        sel?.addRange(range)
      }
    }
  }, [show])

  const handleSelectText = (text: string) => {
    setFinalText(text)
    setTimeout(() => {
      if (bubbleRef.current) {
        bubbleRef.current.textContent = text
        bubbleRef.current.focus()
        const el = bubbleRef.current
        if (el) {
          const range = document.createRange()
          range.selectNodeContents(el)
          range.collapse(false)
          const sel = window.getSelection()
          sel?.removeAllRanges()
          sel?.addRange(range)
        }
      }
    }, 0)
  }

  const handleSendClick = () => {
    // 从DOM元素中获取最新的文本内容
    const currentText = bubbleRef.current?.textContent || finalText
    onSend(currentText.trim())
  }

  if (!show) return null

  return (
    <div className="container" style={{ zIndex: 9999 }}>
      <style>{`
        .container { position: fixed; top: 0; left: 0; right: 0; bottom: 0; display: flex; flex-direction: column; background: rgba(0,0,0,0.5); backdrop-filter: blur(10px); }
        .content-area { flex: 1; overflow-y: auto; padding: 20px; padding-bottom: 100px; }
        .section {
          margin-bottom: 20px; animation: fadeIn 0.3s ease-out;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px);} to { opacity: 1; transform: translateY(0);} }
        .section-title { font-size: 14px; color: #fff; margin-bottom: 10px; font-weight: 500; opacity: 0.8; }
        .text-bubble { 
          background: #95EC69; 
          color: #000; 
          padding: 12px 16px; 
          border-radius: 10px; 
          font-size: 16px; 
          line-height: 1.5; 
          display: inline-block;
          max-width: 80vw;
          min-width: 48px;
          word-break: break-all;
          white-space: pre-wrap;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          position: relative;
          margin-left: auto;
          margin-right: 0;
          outline: none;
        }
        .text-bubble:focus { box-shadow: 0 0 0 2px #07C160; }
        .text-bubble::before { display: none; }
        .text-bubble::after { content: ''; position: absolute; right: -8px; top: 10px; width: 0; height: 0; border-top: 8px solid transparent; border-bottom: 8px solid transparent; border-left: 8px solid #95EC69; }
        .text-bubble[contenteditable="true"] { outline: none; cursor: text; transition: all 0.3s ease; }
        .text-bubble[contenteditable="true"]:focus { box-shadow: 0 0 0 2px #07C160; }
        .text-option {
          background: #fff;
          color: #333;
          padding: 12px 16px;
          border-radius: 10px;
          font-size: 14px;
          line-height: 1.5;
          display: inline-block;
          max-width: 80vw;
          min-width: 48px;
          word-break: break-all;
          white-space: pre-wrap;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          position: relative;
          margin-left: auto;
          margin-right: 0;
          margin-bottom: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          border: 1px solid transparent;
        }
        .text-option:hover { background: #fff; transform: translateX(5px); box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .text-option:active { transform: translateX(3px); background: #f0f0f0; }
        .bottom-actions { position: fixed; bottom: 0; left: 0; right: 0; background: rgba(255,255,255,0.98); backdrop-filter: blur(20px); padding: 20px; display: flex; gap: 12px; box-shadow: 0 -2px 20px rgba(0,0,0,0.1); }
        .btn { flex: 1; padding: 14px 0; border: none; border-radius: 25px; font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; }
        .btn-cancel { background: #f2f2f2; color: #666; }
        .btn-cancel:hover { background: #e5e5e5; }
        .btn-send { background: #07C160; color: white; }
        .btn-send:hover { background: #06ad56; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(7,193,96,0.3); }
        .btn:active { transform: scale(0.98); }
        .highlight { animation: pulse 0.3s ease; }
        @keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.02); } 100% { transform: scale(1); } }
      `}</style>
      <div className="content-area">
        <div className="section">
          <div className="section-title">要发送的消息：</div>
          <div
            className="text-bubble"
            contentEditable
            suppressContentEditableWarning
            id="finalTextEdit"
            ref={bubbleRef}
            tabIndex={0}
            style={{ minHeight: 48 }}
          >{finalText}</div>
        </div>
        <div className="section">
          <div className="section-title">候选文本</div>
          {error && <div className="text-option" style={{ color: '#ef4444' }}>{error}</div>}
          {(() => {
            // 过滤相邻重复，首项为 originText
            const all = [originText, ...aiVariants.slice(0, aiCount)]
            const filtered: string[] = []
            all.forEach((ai, i) => {
              if (i === 0 || ai !== all[i - 1]) {
                filtered.push(ai)
              }
            })
            return filtered.map((ai, i) => (
              <div className="text-option" key={i} onClick={() => handleSelectText(ai)}>{ai}↑</div>
            ))
          })()}
          {loading && <div className="text-option">小天尝试理解分析中</div>}

        </div>
      </div>
      <div className="bottom-actions">
        <button className="btn btn-cancel" onClick={onCancel}>取消</button>
        <button className="btn btn-send" onClick={handleSendClick}>发送</button>
      </div>
    </div>
  )
}
// ========== END ========== //

const VoiceInput: React.FC<VoiceInputProps> = ({ onResult, onError }) => {
  const {
    isRecording,
    hasPermission,
    error,
    startRecording,
    stopRecording,
    cancelRecording,
    setOnResult,
    isSupported,
    audioLevels,
  } = useVoiceRecording()

  // 权限状态
  const [permissionChecked, setPermissionChecked] = useState(false)
  // 是否显示全屏面板
  const [showPanel, setShowPanel] = useState(false)
  // 当前滑动到的区域
  const [panelStatus, setPanelStatus] = useState<PanelStatus>('')
  // 用 ref 记录松手时的最终操作意图，避免 showPanel 状态提前变更导致丢失
  const panelStatusRef = useRef<PanelStatus>('')
  // 语音转文字内容
  const [voiceText, setVoiceText] = useState('')
  // 是否显示转文字编辑框
  const [showTextEdit, setShowTextEdit] = useState(false)
  // 按钮按下状态
  const isHoldingRef = useRef(false)
  // 记录触摸/鼠标起始点
  const startPointRef = useRef<{ x: number; y: number } | null>(null)
  // 超时定时器，用于处理异步操作延迟
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 检测是否为静音状态
  const isSilent = React.useMemo(() => {
    if (!audioLevels || audioLevels.length === 0) return false
    const minLevel = Math.min(...audioLevels)
    const maxLevel = Math.max(...audioLevels)
    const levelVariance = maxLevel - minLevel
    const averageLevel = audioLevels.reduce((sum, level) => sum + level, 0) / audioLevels.length
    return levelVariance < 5 || averageLevel < 25
  }, [audioLevels])

  // 设置语音识别回调
  React.useEffect(() => {
    setOnResult((text) => {
      console.log('[VoiceInput] setOnResult 回调触发，text:', text, 'panelStatusRef:', panelStatusRef.current)
      setVoiceText(text)

      // 清除超时定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }

      // 只依赖 panelStatusRef 判断
      if (panelStatusRef.current === PANEL_STATUS.SEND) {
        console.log('[VoiceInput] 发送状态，调用 onResult:', text)
        onResult(text)
        setVoiceText('')
        setPanelStatus('')
        panelStatusRef.current = ''
      }
      if (panelStatusRef.current === PANEL_STATUS.TEXT) {
        console.log('[VoiceInput] 调整文字状态，检查文字内容，text:', text)
        // 只有识别出文字时才显示调整文字面板
        if (text && text.trim()) {
          console.log('[VoiceInput] 识别出文字，弹出调整文字编辑框，text:', text)
          setShowTextEdit(true)
        } else {
          console.log('[VoiceInput] 未识别出文字，显示提示')
          showToast({ type: 'text', content: '未识别到语音内容' })
        }
        setPanelStatus('')
        panelStatusRef.current = ''
      }
    })
  }, [setOnResult, onResult])

  // 组件卸载时清理定时器
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [])

  // 权限检查（只检查，不弹窗）
  const checkPermissionOnly = useCallback(async (): Promise<boolean> => {
    try {
      const result = await checkMicrophonePermission()
      return result.hasPermission || false
    } catch {
      return false
    }
  }, [])

  // 权限检查和申请（只在点击按钮时调用）
  const checkAndRequestPermission = useCallback(async (): Promise<boolean> => {
    try {
      const has = await checkPermissionOnly()
      if (has) return true
      const granted = await ensureMicrophonePermission(true)
      if (!granted) {
        showToast({ type: 'error', content: '麦克风权限被拒绝，无法录音。请在设置中开启麦克风权限。' })
        onError?.('麦克风权限被拒绝，无法录音')
        return false
      }
      return has
    } catch {
      showToast({ type: 'error', content: '权限检查失败，请重试' })
      onError?.('权限检查失败')
      return false
    }
  }, [onError, checkPermissionOnly])

  // 鼠标/触摸按下
  const handlePressStart = async (e: React.MouseEvent | React.TouchEvent) => {
    e.stopPropagation()
    if (!isSupported()) {
      onError?.('浏览器不支持语音录制')
      return
    }

    // 立即设置状态，避免快速松手时状态未初始化
    isHoldingRef.current = true
    setPanelStatus(PANEL_STATUS.SEND)
    panelStatusRef.current = PANEL_STATUS.SEND

    // 先显示面板，提供即时反馈
    setShowPanel(true)
    // 记录起始点
    const point = 'touches' in e ? e.touches[0] : e
    startPointRef.current = { x: point.clientX, y: point.clientY }

    // 异步检查权限和开始录音
    try {
      console.log('[VoiceInput] 开始检查权限')
      const has = await checkAndRequestPermission()
      setPermissionChecked(true)

      // 检查是否在权限检查期间用户已经松手
      if (!isHoldingRef.current) {
        console.log('[VoiceInput] 权限检查期间用户已经松手，关闭面板')
        setShowPanel(false)
        setPanelStatus('')
        panelStatusRef.current = ''
        return
      }

      if (!has) {
        console.log('[VoiceInput] 权限检查失败，重置状态')
        // 权限失败时重置状态
        isHoldingRef.current = false
        setShowPanel(false)
        setPanelStatus('')
        panelStatusRef.current = ''
        return
      }

      // 有权限，开始录音
      console.log('[VoiceInput] 开始录音')
      await startRecording()
      console.log('[VoiceInput] 录音中')

      // 再次检查是否在录音启动期间用户已经松手
      if (!isHoldingRef.current) {
        console.log('[VoiceInput] 录音启动期间用户已经松手，停止录音')
        await stopRecording()
        setShowPanel(false)
        setPanelStatus('')
        panelStatusRef.current = ''
        return
      }

    } catch (error) {
      console.error('启动录音失败:', error)
      isHoldingRef.current = false
      setShowPanel(false)
      setPanelStatus('')
      panelStatusRef.current = ''
      onError?.('启动录音失败')
      return
    }

    // 仅PC端：全局监听 mouseup，确保松开时能正确关闭面板
    if (!('ontouchstart' in window)) {
      window.addEventListener('mouseup', handleGlobalMouseUp)
    }
  }

  // PC端全局 mouseup 处理
  const handleGlobalMouseUp = (e: MouseEvent) => {
    if (!isHoldingRef.current) return
    handlePressEnd()
    window.removeEventListener('mouseup', handleGlobalMouseUp)
  }

  // 松开按钮/手指
  const handlePressEnd = async (e?: React.MouseEvent | React.TouchEvent) => {
    if (!isHoldingRef.current) return

    console.log('[VoiceInput] handlePressEnd 开始，isRecording:', isRecording, 'panelStatus:', panelStatus, 'panelStatusRef:', panelStatusRef.current)

    isHoldingRef.current = false
    setShowPanel(false)
    if (!('ontouchstart' in window)) {
      window.removeEventListener('mouseup', handleGlobalMouseUp)
    }

    // 使用当前的 panelStatus，如果为空则使用 panelStatusRef 的值（快速松手的情况）
    const currentStatus = panelStatus || panelStatusRef.current

    if (currentStatus === PANEL_STATUS.CANCEL) {
      console.log('[VoiceInput] 取消状态，调用 cancelRecording')
      await cancelRecording()
      showToast({ type: 'text', content: '已取消发送' })
      setPanelStatus('')
      panelStatusRef.current = ''
      return
    }

    // 如果录音还没开始（权限检查阶段或快速松手），直接重置状态
    if (!isRecording) {
      console.log('[VoiceInput] 录音未开始，直接重置状态')
      setPanelStatus('')
      panelStatusRef.current = ''
      return
    }

    // 记录松手时的最终意图，优先使用当前状态
    panelStatusRef.current = currentStatus
    // 发送和转文字都先停止录音，等待识别回调处理
    console.log('[VoiceInput] handlePressEnd 停止录音，currentStatus:', currentStatus, 'panelStatusRef:', panelStatusRef.current)

    try {
      await stopRecording()

      // 设置超时机制，防止识别结果回调丢失导致状态无法清理
      timeoutRef.current = setTimeout(() => {
        console.log('[VoiceInput] 识别结果超时，强制清理状态')
        setPanelStatus('')
        panelStatusRef.current = ''
        setVoiceText('')
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
          timeoutRef.current = null
        }
      }, 5000) // 5秒超时

    } catch (error) {
      console.error('[VoiceInput] 停止录音失败:', error)
      // 即使停止录音失败，也要重置状态
      setPanelStatus('')
      panelStatusRef.current = ''
    }
    // 其余逻辑在 setOnResult 回调中处理
  }

  // 滑动手势处理
  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isHoldingRef.current || !showPanel) return
    const point = 'touches' in e ? e.touches[0] : e
    const { clientX, clientY } = point
    // 获取面板区域
    const panel = document.getElementById('voice-panel')
    if (!panel) return
    const rect = panel.getBoundingClientRect()
    // 区域划分：左1/3为取消，右1/3为转文字，中间为发送
    const x = clientX - rect.left
    const y = clientY - rect.top
    const width = rect.width
    let status: PanelStatus = PANEL_STATUS.SEND
    if (x < width / 3) status = PANEL_STATUS.CANCEL
    else if (x > (width * 2) / 3) status = PANEL_STATUS.TEXT
    else status = PANEL_STATUS.SEND

    // 同时更新两个状态引用，确保状态同步
    setPanelStatus(status)
    panelStatusRef.current = status
  }

  // 编辑转文字内容后手动发送
  const handleTextSend = () => {
    console.log('[VoiceInput] handleTextSend, voiceText:', voiceText)
    if (voiceText) onResult(voiceText)
    setShowTextEdit(false)
    setVoiceText('')
  }

  // 关闭转文字编辑
  const handleTextCancel = () => {
    setShowTextEdit(false)
    setVoiceText('')
  }

  // 主按钮渲染
  const renderMainButton = () => (
    <button
      // className="flex-1  w-full rounded bg-white px-4 py-2 text-center text-base transition-colors select-none border-gray-200 "
      className="flex-1 w-full rounded bg-transparent px-4 py-1.5 text-center text-base transition-colors select-none border border-transparent min-h-[40px] flex items-center justify-center"
      onMouseDown={handlePressStart}
      // onMouseUp={handlePressEnd}  // 移除，改为全局 mouseup
      // onMouseLeave={handlePressEnd} // 移除，防止提前关闭
      onTouchStart={handlePressStart}
      onTouchEnd={handlePressEnd}
      onTouchCancel={handlePressEnd}
      onTouchMove={handleMove}
      onMouseMove={handleMove}
      type="button"
    >
      按住 说话
    </button>
  )

  // 全屏语音面板
  const renderVoicePanel = () => (
    <div
      id="voice-panel"
      className="fixed inset-0 z-50 flex flex-col items-center justify-end bg-black/60"
      style={{ touchAction: 'none' }}
      onMouseUp={handlePressEnd}
      onTouchEnd={handlePressEnd}
      onMouseMove={handleMove}
      onTouchMove={handleMove}
    >
      {/* 全局样式插入 */}
      <style>{waveformStyle}</style>

      {/* 绿色语音气泡波形动画 */}
      <div className="waveform-container flex flex-col items-center">
        <div className="waveform-bubble flex flex-col items-center">
          <div className="waveform">
            {Array.from({ length: 15 }).map((_, i) => (
              <div key={i} className={`bar ${isSilent ? 'bar-silent' : ''}`} />
            ))}
          </div>
        </div>
        <div className="mt-4 text-2xl text-gray-300 font-bold">
          {panelStatus === PANEL_STATUS.CANCEL ? '松开取消' :
            panelStatus === PANEL_STATUS.TEXT ? '松开调整文字' :
              '松开发送'}
        </div>
      </div>

      {/* 弧形控制区域容器 */}
      <div className="relative w-full flex justify-center items-end" style={{ height: '25vh', minHeight: '150px' }}>
        {/* 弧形背景 */}
        <div className={`controls-arc-bg transition-colors duration-200 ${panelStatus === PANEL_STATUS.SEND ? 'send-arc-active' : ''}`}></div>

        {/* 圆形按钮：取消和转文字 */}
        <div className="absolute top-1 left-0 right-0 flex justify-between px-4 z-10">
          {/* 取消按钮 */}
          <div className={`circular-button circular-button-cancel ${panelStatus === PANEL_STATUS.CANCEL ? 'active' : ''}`}>
            取消
          </div>

          {/* 调整文字按钮 */}
          <div className={`circular-button circular-button-text ${panelStatus === PANEL_STATUS.TEXT ? 'active' : ''}`}>
            调整文字
          </div>
        </div>

        {/* 发送文字标签 */}
        <div className={`absolute bottom-16 text-2xl font-bold z-10 transition-colors duration-200 ${panelStatus === PANEL_STATUS.SEND ? 'text-white' : 'text-black'}`}>
          发送
        </div>
      </div>
    </div>
  )

  return (
    <>
      {renderMainButton()}
      {showPanel && createPortal(renderVoicePanel(), document.body)}
      {showTextEdit && createPortal(
        <VoiceTextEditModal
          show={showTextEdit}
          originText={voiceText}
          onSend={(finalText) => {
            if (finalText) onResult(finalText)
            setShowTextEdit(false)
            setVoiceText('')
          }}
          onCancel={() => {
            setShowTextEdit(false)
            setVoiceText('')
          }}
        />,
        document.body
      )}
    </>
  )
}

export default VoiceInput 