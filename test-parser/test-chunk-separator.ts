import { TextStreamParser } from '../src/utils/TextStreamParser'

// 测试新的分隔符功能
function testChunkSeparator() {
  console.log('=== 测试新分隔符 --tina-chunk-- ===')
  
  const parser = new TextStreamParser('test')
  
  // 测试1: 基本分隔符功能
  console.log('\n测试1: 基本分隔符功能')
  const input1 = 'Hello world--tina-chunk--This is second chunk'
  const results1 = parser.processText(input1, true)
  console.log('输入:', input1)
  console.log('输出:', results1)
  
  // 测试2: 包含表格的内容（之前会误拆分的场景）
  console.log('\n测试2: 包含表格的内容')
  const parser2 = new TextStreamParser('test2')
  const input2 = `这是一个表格：

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2<br>换行 | 数据3 |
| 更多数据 | 内容 | 结束 |

--tina-chunk--

这是第二段内容，包含代码块：

\`\`\`javascript
function test() {
  console.log("Hello<br>World");
  return true;
}
\`\`\`

--tina-chunk--

第三段内容结束。`
  
  const results2 = parser2.processText(input2, true)
  console.log('输入:', input2)
  console.log('输出:')
  results2.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.message_id_type}: ${JSON.stringify(result.content)}`)
  })
  
  // 测试3: 流式处理
  console.log('\n测试3: 流式处理')
  const parser3 = new TextStreamParser('test3')
  
  const chunks = [
    '第一段内容',
    '--tina-chu',
    'nk--第二段',
    '内容--tina-chunk--',
    '第三段内容'
  ]
  
  let allResults: any[] = []
  chunks.forEach((chunk, index) => {
    const isLast = index === chunks.length - 1
    const results = parser3.processText(chunk, isLast)
    allResults.push(...results)
    console.log(`处理块 ${index + 1}: "${chunk}"`)
    console.log('  结果:', results)
  })
  
  console.log('流式处理最终结果:')
  allResults.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.message_id_type}: ${JSON.stringify(result.content)}`)
  })
  
  // 测试4: 与XML标签混合
  console.log('\n测试4: 与XML标签混合')
  const parser4 = new TextStreamParser('test4')
  const input4 = `普通文本内容--tina-chunk--<tina_task>任务内容</tina_task>--tina-chunk--更多文本`
  const results4 = parser4.processText(input4, true)
  console.log('输入:', input4)
  console.log('输出:')
  results4.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.message_id_type}: ${JSON.stringify(result.content)}`)
  })
  
  // 测试5: 与voice块混合
  console.log('\n测试5: 与voice块混合')
  const parser5 = new TextStreamParser('test5')
  const input5 = `文本内容--tina-chunk--\`\`\`voice
语音内容
\`\`\`--tina-chunk--结束文本`
  const results5 = parser5.processText(input5, true)
  console.log('输入:', input5)
  console.log('输出:')
  results5.forEach((result, index) => {
    console.log(`  ${index + 1}. ${result.message_id_type}: ${JSON.stringify(result.content)}`)
  })
}

// 运行测试
testChunkSeparator()
