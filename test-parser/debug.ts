import { TextStreamParser } from '../src/utils/TextStreamParser'

// 调试流式处理
console.log('=== 调试流式处理 ===')
const parser = new TextStreamParser('test3')

const chunks = [
  '第一段内容',
  '--tina-chu',
  'nk--第二段',
  '内容--tina-chunk--',
  '第三段内容'
]

let allResults: any[] = []
chunks.forEach((chunk, index) => {
  const isLast = index === chunks.length - 1
  const results = parser.processText(chunk, isLast)
  allResults.push(...results)
  console.log(`处理块 ${index + 1}: "${chunk}"`)
  console.log('  结果:', results)
})

console.log('\n所有结果:')
allResults.forEach((result, index) => {
  console.log(`  ${index + 1}. ${result.message_id_type}: ${JSON.stringify(result.content)}`)
})

// 调试XML混合
console.log('\n=== 调试XML混合 ===')
const parser4 = new TextStreamParser('test4')
const input4 = '普通文本内容--tina-chunk--<tina_task>任务内容</tina_task>--tina-chunk--更多文本'
const results4 = parser4.processText(input4, true)
console.log('输入:', input4)
console.log('输出:')
results4.forEach((result, index) => {
  console.log(`  ${index + 1}. ${result.message_id_type}: ${JSON.stringify(result.content)}`)
})
